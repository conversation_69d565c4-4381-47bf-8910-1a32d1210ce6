import { type MigrationInterface, type QueryRunner } from 'typeorm';

export class InitQuickplay1756351746068 implements MigrationInterface {
	name = 'InitQuickplay1756351746068';

	public async up(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`CREATE TABLE "quickplay" (
			"qp_id" character varying(50) NOT NULL,
			"game_key" character varying(50) NOT NULL,
			"platform" character varying(20) NOT NULL,
			"device_id" character varying(100) NOT NULL,
			"ip_address" character varying(50) NOT NULL,
			"created_at" TIMESTAMP NOT NULL DEFAULT now(), 
			"updated_at" TIMESTAMP NOT NULL DEFAULT now(),
			CONSTRAINT "PK_67274b82fd9f0be11c90ae2bcf2" PRIMARY KEY ("qp_id"))`);
		await queryRunner.query(`CREATE UNIQUE INDEX 
			"quickplay_qp_id_key" ON "quickplay" ("qp_id") `);
	}

	public async down(queryRunner: QueryRunner): Promise<void> {
		await queryRunner.query(`DROP INDEX 
			"public"."quickplay_qp_id_key"`);
		await queryRunner.query(`DROP TABLE 
			"quickplay"`);
	}
}
